<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - وكالة العقارات المتميزة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .settings-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: #3498db;
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-content {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-left: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #3498db;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .container {
                padding: 0 0.5rem;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #7f8c8d;
        }

        .loading.show {
            display: block;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            display: inline-block;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ إعدادات النظام</h1>
        <div>
            <button type="button" class="btn btn-success" onclick="saveAllSettings()">حفظ جميع الإعدادات</button>
            <button type="button" class="btn btn-secondary" onclick="window.close()">إغلاق</button>
        </div>
    </div>

    <div class="container">
        <div class="alert alert-info">
            <strong>تنبيه:</strong> تأكد من حفظ الإعدادات بعد إجراء أي تغييرات. بعض الإعدادات قد تتطلب إعادة تحميل الصفحة.
        </div>

        <!-- إحصائيات النظام -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProperties">0</div>
                <div class="stat-label">إجمالي العقارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">0</div>
                <div class="stat-label">إجمالي الرسائل</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="storageUsed">0</div>
                <div class="stat-label">مساحة التخزين المستخدمة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="lastUpdate">--</div>
                <div class="stat-label">آخر تحديث</div>
            </div>
        </div>

        <!-- إعدادات الأمان -->
        <div class="settings-section">
            <div class="section-header">
                🔒 إعدادات الأمان
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label for="currentPassword">كلمة المرور الحالية</label>
                    <input type="password" id="currentPassword" placeholder="أدخل كلمة المرور الحالية">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="newPassword">كلمة المرور الجديدة</label>
                        <input type="password" id="newPassword" placeholder="كلمة مرور جديدة">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">تأكيد كلمة المرور</label>
                        <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                    </div>
                </div>
                <button type="button" class="btn btn-warning" onclick="changePassword()">تغيير كلمة المرور</button>
            </div>
        </div>

        <!-- إعدادات الموقع العامة -->
        <div class="settings-section">
            <div class="section-header">
                🌐 إعدادات الموقع العامة
            </div>
            <div class="section-content">
                <div class="form-row">
                    <div class="form-group">
                        <label for="siteName">اسم الموقع</label>
                        <input type="text" id="siteName" value="وكالة العقارات المتميزة">
                    </div>
                    <div class="form-group">
                        <label for="siteLanguage">لغة الموقع</label>
                        <select id="siteLanguage">
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="siteDescription">وصف الموقع</label>
                    <textarea id="siteDescription" rows="3">وكالة عقارية متميزة تقدم أفضل الخدمات العقارية</textarea>
                </div>
                <div class="form-group">
                    <label for="siteKeywords">الكلمات المفتاحية (مفصولة بفواصل)</label>
                    <input type="text" id="siteKeywords" value="عقارات, بيع, شراء, إيجار, فلل, شقق">
                </div>
            </div>
        </div>

        <!-- إعدادات العرض -->
        <div class="settings-section">
            <div class="section-header">
                🎨 إعدادات العرض
            </div>
            <div class="section-content">
                <div class="form-row">
                    <div class="form-group">
                        <label for="propertiesPerPage">عدد العقارات في الصفحة</label>
                        <select id="propertiesPerPage">
                            <option value="6">6 عقارات</option>
                            <option value="9">9 عقارات</option>
                            <option value="12">12 عقار</option>
                            <option value="15">15 عقار</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="defaultCurrency">العملة الافتراضية</label>
                        <select id="defaultCurrency">
                            <option value="ريال">ريال سعودي</option>
                            <option value="درهم">درهم إماراتي</option>
                            <option value="دولار">دولار أمريكي</option>
                            <option value="يورو">يورو</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="showPrices"> عرض الأسعار للزوار
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableAnimations"> تفعيل الرسوم المتحركة
                    </label>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="settings-section">
            <div class="section-header">
                🔔 إعدادات الإشعارات
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="emailNotifications"> إشعارات البريد الإلكتروني
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="newMessageNotifications"> إشعار عند وصول رسالة جديدة
                    </label>
                </div>
                <div class="form-group">
                    <label for="adminEmail">البريد الإلكتروني للإشعارات</label>
                    <input type="email" id="adminEmail" value="<EMAIL>">
                </div>
            </div>
        </div>

        <!-- إعدادات النسخ الاحتياطي -->
        <div class="settings-section">
            <div class="section-header">
                💾 إعدادات النسخ الاحتياطي
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoBackup"> النسخ الاحتياطي التلقائي
                    </label>
                </div>
                <div class="form-group">
                    <label for="backupFrequency">تكرار النسخ الاحتياطي</label>
                    <select id="backupFrequency">
                        <option value="daily">يومياً</option>
                        <option value="weekly">أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="maxBackups">عدد النسخ الاحتياطية المحفوظة</label>
                    <input type="number" id="maxBackups" value="5" min="1" max="20">
                </div>
            </div>
        </div>

        <!-- إعدادات متقدمة -->
        <div class="settings-section">
            <div class="section-header">
                🔧 إعدادات متقدمة
            </div>
            <div class="section-content">
                <div class="alert alert-warning">
                    <strong>تحذير:</strong> هذه الإعدادات للمستخدمين المتقدمين فقط. التغيير الخاطئ قد يؤثر على عمل النظام.
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="debugMode"> وضع التشخيص (Debug Mode)
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="maintenanceMode"> وضع الصيانة
                    </label>
                </div>
                <div class="form-group">
                    <label for="sessionTimeout">مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" id="sessionTimeout" value="60" min="15" max="480">
                </div>
                <button type="button" class="btn btn-danger" onclick="resetAllSettings()">إعادة تعيين جميع الإعدادات</button>
                <button type="button" class="btn btn-warning" onclick="clearAllData()">مسح جميع البيانات</button>
            </div>
        </div>

        <div class="loading" id="loading">جاري حفظ الإعدادات...</div>
    </div>

    <script>
        // تحميل الإعدادات الحالية
        function loadCurrentSettings() {
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            const siteSettings = JSON.parse(localStorage.getItem('siteSettings') || '{}');

            // تحميل إعدادات الموقع
            if (siteSettings.agencyName) document.getElementById('siteName').value = siteSettings.agencyName;
            if (siteSettings.agencyDescription) document.getElementById('siteDescription').value = siteSettings.agencyDescription;

            // تحميل الإعدادات الأخرى
            if (settings.propertiesPerPage) document.getElementById('propertiesPerPage').value = settings.propertiesPerPage;
            if (settings.defaultCurrency) document.getElementById('defaultCurrency').value = settings.defaultCurrency;
            if (settings.adminEmail) document.getElementById('adminEmail').value = settings.adminEmail;
            if (settings.sessionTimeout) document.getElementById('sessionTimeout').value = settings.sessionTimeout;
            if (settings.maxBackups) document.getElementById('maxBackups').value = settings.maxBackups;
            if (settings.backupFrequency) document.getElementById('backupFrequency').value = settings.backupFrequency;

            // تحميل الخيارات المنطقية
            document.getElementById('showPrices').checked = settings.showPrices !== false;
            document.getElementById('enableAnimations').checked = settings.enableAnimations !== false;
            document.getElementById('emailNotifications').checked = settings.emailNotifications === true;
            document.getElementById('newMessageNotifications').checked = settings.newMessageNotifications === true;
            document.getElementById('autoBackup').checked = settings.autoBackup === true;
            document.getElementById('debugMode').checked = settings.debugMode === true;
            document.getElementById('maintenanceMode').checked = settings.maintenanceMode === true;

            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            const properties = JSON.parse(localStorage.getItem('siteProperties') || '[]');
            const messages = JSON.parse(localStorage.getItem('contacts') || '[]');
            const settings = JSON.parse(localStorage.getItem('siteSettings') || '{}');

            document.getElementById('totalProperties').textContent = properties.length;
            document.getElementById('totalMessages').textContent = messages.length;

            // حساب مساحة التخزين المستخدمة
            const storageSize = new Blob([JSON.stringify({properties, messages, settings})]).size;
            document.getElementById('storageUsed').textContent = (storageSize / 1024).toFixed(1) + ' KB';

            // آخر تحديث
            if (settings.lastUpdated) {
                document.getElementById('lastUpdate').textContent = new Date(settings.lastUpdated).toLocaleDateString('ar-SA');
            }
        }

        // حفظ جميع الإعدادات
        function saveAllSettings() {
            const loading = document.getElementById('loading');
            loading.classList.add('show');

            const settings = {
                // إعدادات الموقع
                siteName: document.getElementById('siteName').value,
                siteLanguage: document.getElementById('siteLanguage').value,
                siteDescription: document.getElementById('siteDescription').value,
                siteKeywords: document.getElementById('siteKeywords').value,

                // إعدادات العرض
                propertiesPerPage: document.getElementById('propertiesPerPage').value,
                defaultCurrency: document.getElementById('defaultCurrency').value,
                showPrices: document.getElementById('showPrices').checked,
                enableAnimations: document.getElementById('enableAnimations').checked,

                // إعدادات الإشعارات
                emailNotifications: document.getElementById('emailNotifications').checked,
                newMessageNotifications: document.getElementById('newMessageNotifications').checked,
                adminEmail: document.getElementById('adminEmail').value,

                // إعدادات النسخ الاحتياطي
                autoBackup: document.getElementById('autoBackup').checked,
                backupFrequency: document.getElementById('backupFrequency').value,
                maxBackups: parseInt(document.getElementById('maxBackups').value),

                // إعدادات متقدمة
                debugMode: document.getElementById('debugMode').checked,
                maintenanceMode: document.getElementById('maintenanceMode').checked,
                sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),

                lastUpdated: new Date().toISOString()
            };

            setTimeout(() => {
                localStorage.setItem('systemSettings', JSON.stringify(settings));

                // تحديث إعدادات الموقع أيضاً
                const siteSettings = JSON.parse(localStorage.getItem('siteSettings') || '{}');
                siteSettings.agencyName = settings.siteName;
                siteSettings.agencyDescription = settings.siteDescription;
                localStorage.setItem('siteSettings', JSON.stringify(siteSettings));

                loading.classList.remove('show');

                const alert = document.createElement('div');
                alert.className = 'alert alert-success';
                alert.innerHTML = '<strong>تم الحفظ!</strong> تم حفظ جميع الإعدادات بنجاح.';
                document.querySelector('.container').insertBefore(alert, document.querySelector('.stats-grid'));

                setTimeout(() => alert.remove(), 3000);

                console.log('تم حفظ الإعدادات:', settings);
            }, 1000);
        }

        // تغيير كلمة المرور
        function changePassword() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('يرجى ملء جميع حقول كلمة المرور');
                return;
            }

            if (currentPassword !== 'admin123') {
                alert('كلمة المرور الحالية غير صحيحة');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
                return;
            }

            if (newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            alert('تم تغيير كلمة المرور بنجاح!\nملاحظة: في النظام الحقيقي، يجب تحديث كلمة المرور في قاعدة البيانات.');

            // مسح الحقول
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }

        // إعادة تعيين جميع الإعدادات
        function resetAllSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع الإعدادات المخصصة.')) {
                localStorage.removeItem('systemSettings');
                alert('تم إعادة تعيين جميع الإعدادات');
                location.reload();
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('تحذير: هذا الإجراء سيحذف جميع البيانات (العقارات، الرسائل، الإعدادات).\nهل أنت متأكد؟')) {
                if (confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد تماماً؟')) {
                    localStorage.clear();
                    alert('تم مسح جميع البيانات');
                    window.location.href = 'admin-login.html';
                }
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSettings();
            console.log('⚙️ صفحة الإعدادات جاهزة');
        });
    </script>
</body>
</html>
