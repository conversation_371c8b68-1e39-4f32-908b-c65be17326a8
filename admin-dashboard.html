<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المسؤول - وكالة العقارات المتميزة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        /* الشريط العلوي */
        .top-bar {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .admin-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .admin-avatar {
            width: 40px;
            height: 40px;
            background: #3498db;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 70px;
            width: 250px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
        }

        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }

        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #2c3e50;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #ecf0f1;
            border-right-color: #3498db;
            color: #3498db;
        }

        .sidebar-menu .icon {
            margin-left: 10px;
            font-size: 1.1rem;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #7f8c8d;
        }

        /* البطاقات الإحصائية */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        /* أقسام المحتوى */
        .content-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
        }

        .section-content {
            padding: 1.5rem;
        }

        /* الأزرار */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        /* الجداول */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        /* الرسائل */
        .alert {
            padding: 12px 16px;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* التجاوب */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .top-bar {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* تأثيرات التحميل */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .loading::after {
            content: '';
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            display: inline-block;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* إضافة classes للعناصر المخصصة */
        .admin-login-time {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .settings-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }

        .settings-card.security {
            border-left: 4px solid #3498db;
        }

        .settings-card.website {
            border-left: 4px solid #27ae60;
        }

        .settings-card.display {
            border-left: 4px solid #f39c12;
        }

        .settings-card.notifications {
            border-left: 4px solid #e74c3c;
        }

        .settings-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .settings-card p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- الشريط العلوي -->
    <div class="top-bar">
        <div class="admin-info">
            <div class="admin-avatar">👤</div>
            <div>
                <div><strong id="adminName">المسؤول</strong></div>
                <div class="admin-login-time" id="loginTime">آخر دخول: الآن</div>
            </div>
        </div>
        <div>
            <a href="index.html" class="btn btn-primary" target="_blank">عرض الموقع</a>
            <button type="button" class="logout-btn" onclick="logout()">تسجيل خروج</button>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="#dashboard" class="menu-link active" onclick="showSection('dashboard')">
                <span class="icon">📊</span>لوحة التحكم
            </a></li>
            <li><a href="#properties" class="menu-link" onclick="showSection('properties')">
                <span class="icon">🏠</span>إدارة العقارات
            </a></li>
            <li><a href="#content" class="menu-link" onclick="showSection('content')">
                <span class="icon">📝</span>إدارة المحتوى
            </a></li>
            <li><a href="#messages" class="menu-link" onclick="showSection('messages')">
                <span class="icon">💬</span>الرسائل
            </a></li>
            <li><a href="#settings" class="menu-link" onclick="showSection('settings')">
                <span class="icon">⚙️</span>الإعدادات
            </a></li>
            <li><a href="#backup" class="menu-link" onclick="showSection('backup')">
                <span class="icon">💾</span>النسخ الاحتياطي
            </a></li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- قسم لوحة التحكم -->
        <div id="dashboard" class="content-section active">
            <div class="page-header">
                <h1 class="page-title">لوحة التحكم</h1>
                <p class="page-subtitle">مرحباً بك في لوحة تحكم وكالة العقارات المتميزة</p>
            </div>

            <!-- البطاقات الإحصائية -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🏠</div>
                    <div class="stat-number" id="totalProperties">3</div>
                    <div class="stat-label">إجمالي العقارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-number" id="totalMessages">0</div>
                    <div class="stat-label">الرسائل الجديدة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number" id="totalVisitors">--</div>
                    <div class="stat-label">الزوار اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📈</div>
                    <div class="stat-number" id="conversionRate">--</div>
                    <div class="stat-label">معدل التحويل</div>
                </div>
            </div>

            <!-- الأنشطة الأخيرة -->
            <div class="content-section active">
                <div class="section-header">
                    <h2 class="section-title">الأنشطة الأخيرة</h2>
                </div>
                <div class="section-content">
                    <div id="recentActivities">
                        <div class="alert alert-info">
                            مرحباً! هذه هي المرة الأولى التي تدخل فيها إلى لوحة التحكم.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة العقارات -->
        <div id="properties" class="content-section">
            <div class="section-header">
                <h2 class="section-title">إدارة العقارات</h2>
                <button type="button" class="btn btn-primary" onclick="showAddPropertyForm()">إضافة عقار جديد</button>
            </div>
            <div class="section-content">
                <div id="propertiesList">
                    <div class="loading">جاري تحميل العقارات...</div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة المحتوى -->
        <div id="content" class="content-section">
            <div class="section-header">
                <h2 class="section-title">إدارة المحتوى</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    هنا يمكنك تعديل محتوى الموقع مثل النصوص والصور والمعلومات العامة.
                </div>
                <button type="button" class="btn btn-primary" onclick="editSiteContent()">تعديل محتوى الموقع</button>
            </div>
        </div>

        <!-- قسم الرسائل -->
        <div id="messages" class="content-section">
            <div class="section-header">
                <h2 class="section-title">رسائل العملاء</h2>
                <button type="button" class="btn btn-warning" onclick="refreshMessages()">تحديث</button>
            </div>
            <div class="section-content">
                <div id="messagesList">
                    <div class="loading">جاري تحميل الرسائل...</div>
                </div>
            </div>
        </div>

        <!-- قسم الإعدادات -->
        <div id="settings" class="content-section">
            <div class="section-header">
                <h2 class="section-title">إعدادات النظام</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    <strong>إعدادات شاملة للنظام:</strong> يمكنك تخصيص جميع جوانب الموقع ولوحة التحكم من هنا.
                </div>

                <div class="settings-grid">
                    <div class="settings-card security">
                        <h4>🔒 الأمان</h4>
                        <p>تغيير كلمة المرور وإعدادات الأمان</p>
                    </div>
                    <div class="settings-card website">
                        <h4>🌐 الموقع</h4>
                        <p>إعدادات عامة للموقع واللغة</p>
                    </div>
                    <div class="settings-card display">
                        <h4>🎨 العرض</h4>
                        <p>تخصيص طريقة عرض المحتوى</p>
                    </div>
                    <div class="settings-card notifications">
                        <h4>🔔 الإشعارات</h4>
                        <p>إدارة الإشعارات والتنبيهات</p>
                    </div>
                </div>

                <button type="button" class="btn btn-primary" onclick="openSettings()">فتح صفحة الإعدادات الكاملة</button>
                <button type="button" class="btn btn-secondary" onclick="showQuickSettings()">الإعدادات السريعة</button>
            </div>
        </div>

        <!-- قسم النسخ الاحتياطي -->
        <div id="backup" class="content-section">
            <div class="section-header">
                <h2 class="section-title">النسخ الاحتياطي</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    إنشاء وإدارة النسخ الاحتياطية لبيانات الموقع.
                </div>
                <button type="button" class="btn btn-success" onclick="createBackup()">إنشاء نسخة احتياطية</button>
                <button type="button" class="btn btn-warning" onclick="restoreBackup()">استعادة نسخة احتياطية</button>
            </div>
        </div>
    </div>

    <script>
        // التحقق من تسجيل الدخول
        function checkAuthentication() {
            const session = localStorage.getItem('adminSession');
            if (!session) {
                window.location.href = 'admin-login.html';
                return false;
            }

            try {
                const sessionData = JSON.parse(session);
                if (!sessionData.isLoggedIn) {
                    window.location.href = 'admin-login.html';
                    return false;
                }

                // تحديث معلومات المسؤول
                document.getElementById('adminName').textContent = sessionData.username;
                document.getElementById('loginTime').textContent =
                    'آخر دخول: ' + new Date(sessionData.loginTime).toLocaleString('ar-SA');

                return true;
            } catch (error) {
                localStorage.removeItem('adminSession');
                window.location.href = 'admin-login.html';
                return false;
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('adminSession');
                window.location.href = 'admin-login.html';
            }
        }

        // عرض قسم معين
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // إزالة التحديد من جميع روابط القائمة
            document.querySelectorAll('.menu-link').forEach(link => {
                link.classList.remove('active');
            });

            // عرض القسم المطلوب
            document.getElementById(sectionId).classList.add('active');

            // تحديد الرابط النشط
            document.querySelector(`[onclick="showSection('${sectionId}')"]`).classList.add('active');

            // تحميل محتوى القسم
            loadSectionContent(sectionId);
        }

        // تحميل محتوى القسم
        function loadSectionContent(sectionId) {
            switch(sectionId) {
                case 'properties':
                    loadProperties();
                    break;
                case 'messages':
                    loadMessages();
                    break;
                case 'dashboard':
                    updateStats();
                    break;
            }
        }

        // تحديث الإحصائيات
        function updateStats() {
            // عدد العقارات
            const properties = JSON.parse(localStorage.getItem('siteProperties') || '[]');
            document.getElementById('totalProperties').textContent = properties.length;

            // عدد الرسائل
            const messages = JSON.parse(localStorage.getItem('contacts') || '[]');
            document.getElementById('totalMessages').textContent = messages.length;

            // إحصائيات أخرى (يمكن تطويرها لاحقاً)
            document.getElementById('totalVisitors').textContent = Math.floor(Math.random() * 100) + 50;
            document.getElementById('conversionRate').textContent = (Math.random() * 10 + 5).toFixed(1) + '%';
        }

        // تحميل العقارات
        function loadProperties() {
            const propertiesList = document.getElementById('propertiesList');
            const properties = JSON.parse(localStorage.getItem('siteProperties') || '[]');

            if (properties.length === 0) {
                propertiesList.innerHTML = '<div class="alert alert-info">لا توجد عقارات مضافة بعد.</div>';
                return;
            }

            let html = '<table class="table"><thead><tr><th>العنوان</th><th>السعر</th><th>الموقع</th><th>الإجراءات</th></tr></thead><tbody>';

            properties.forEach((property, index) => {
                html += `
                    <tr>
                        <td>${property.title}</td>
                        <td>${property.price}</td>
                        <td>${property.location}</td>
                        <td>
                            <button class="btn btn-warning" onclick="editProperty(${index})">تعديل</button>
                            <button class="btn btn-danger" onclick="deleteProperty(${index})">حذف</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            propertiesList.innerHTML = html;
        }

        // تحميل الرسائل
        function loadMessages() {
            const messagesList = document.getElementById('messagesList');
            const messages = JSON.parse(localStorage.getItem('contacts') || '[]');

            if (messages.length === 0) {
                messagesList.innerHTML = '<div class="alert alert-info">لا توجد رسائل جديدة.</div>';
                return;
            }

            let html = '<table class="table"><thead><tr><th>الاسم</th><th>البريد</th><th>الهاتف</th><th>التاريخ</th><th>الإجراءات</th></tr></thead><tbody>';

            messages.reverse().forEach((message, index) => {
                html += `
                    <tr>
                        <td>${message.name}</td>
                        <td>${message.email}</td>
                        <td>${message.phone}</td>
                        <td>${new Date(message.timestamp).toLocaleDateString('ar-SA')}</td>
                        <td>
                            <button class="btn btn-primary" onclick="viewMessage(${messages.length - 1 - index})">عرض</button>
                            <button class="btn btn-danger" onclick="deleteMessage(${messages.length - 1 - index})">حذف</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            messagesList.innerHTML = html;
        }

        // الدوال المساعدة (ستتم إضافة التفاصيل لاحقاً)
        function showAddPropertyForm() {
            alert('سيتم فتح نموذج إضافة عقار جديد');
        }

        function editProperty(index) {
            alert(`تعديل العقار رقم ${index + 1}`);
        }

        function deleteProperty(index) {
            if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
                let properties = JSON.parse(localStorage.getItem('siteProperties') || '[]');
                properties.splice(index, 1);
                localStorage.setItem('siteProperties', JSON.stringify(properties));
                loadProperties();
                updateStats();
            }
        }

        function viewMessage(index) {
            const messages = JSON.parse(localStorage.getItem('contacts') || '[]');
            const message = messages[index];
            alert(`رسالة من: ${message.name}\nالبريد: ${message.email}\nالهاتف: ${message.phone}\n\nالرسالة:\n${message.message}`);
        }

        function deleteMessage(index) {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                let messages = JSON.parse(localStorage.getItem('contacts') || '[]');
                messages.splice(index, 1);
                localStorage.setItem('contacts', JSON.stringify(messages));
                loadMessages();
                updateStats();
            }
        }

        function editSiteContent() {
            window.open('admin-content-editor.html', '_blank');
        }

        function refreshMessages() {
            loadMessages();
        }

        function openSettings() {
            window.open('admin-settings.html', '_blank');
        }

        function showQuickSettings() {
            const quickSettings = `
                <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; max-width: 400px; width: 90%;">
                    <h3 style="margin-bottom: 1rem; color: #2c3e50;">⚙️ الإعدادات السريعة</h3>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem;">عدد العقارات في الصفحة:</label>
                        <select id="quickPropertiesCount" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="6">6 عقارات</option>
                            <option value="9">9 عقارات</option>
                            <option value="12">12 عقار</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="quickShowPrices" checked>
                            عرض الأسعار للزوار
                        </label>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="quickAnimations" checked>
                            تفعيل الرسوم المتحركة
                        </label>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="saveQuickSettings()" style="background: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">حفظ</button>
                        <button onclick="closeQuickSettings()" style="background: #95a5a6; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">إلغاء</button>
                    </div>
                </div>
                <div id="quickSettingsOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;" onclick="closeQuickSettings()"></div>
            `;

            document.body.insertAdjacentHTML('beforeend', quickSettings);
        }

        function saveQuickSettings() {
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            settings.propertiesPerPage = document.getElementById('quickPropertiesCount').value;
            settings.showPrices = document.getElementById('quickShowPrices').checked;
            settings.enableAnimations = document.getElementById('quickAnimations').checked;
            settings.lastUpdated = new Date().toISOString();

            localStorage.setItem('systemSettings', JSON.stringify(settings));
            alert('تم حفظ الإعدادات السريعة بنجاح!');
            closeQuickSettings();
        }

        function closeQuickSettings() {
            const overlay = document.getElementById('quickSettingsOverlay');
            if (overlay) {
                overlay.parentElement.remove();
            }
        }

        function createBackup() {
            const data = {
                properties: JSON.parse(localStorage.getItem('siteProperties') || '[]'),
                messages: JSON.parse(localStorage.getItem('contacts') || '[]'),
                settings: JSON.parse(localStorage.getItem('siteSettings') || '{}'),
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            alert('تم إنشاء النسخة الاحتياطية وتحميلها');
        }

        function restoreBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                                localStorage.setItem('siteProperties', JSON.stringify(data.properties || []));
                                localStorage.setItem('contacts', JSON.stringify(data.messages || []));
                                localStorage.setItem('siteSettings', JSON.stringify(data.settings || {}));
                                alert('تم استعادة النسخة الاحتياطية بنجاح');
                                location.reload();
                            }
                        } catch (error) {
                            alert('خطأ في قراءة ملف النسخة الاحتياطية');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // دالة الإعدادات السريعة
        function showQuickSettings() {
            const quickSettingsHTML = `
                <div id="quickSettingsModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); max-width: 400px; width: 90%;">
                        <h3 style="margin-bottom: 1rem; color: #2c3e50;">⚙️ الإعدادات السريعة</h3>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: block; margin-bottom: 0.5rem; color: #2c3e50;">عدد العقارات في الصفحة:</label>
                            <select id="quickPropertiesCount" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                                <option value="6">6 عقارات</option>
                                <option value="9">9 عقارات</option>
                                <option value="12">12 عقار</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: flex; align-items: center; gap: 10px; color: #2c3e50;">
                                <input type="checkbox" id="quickShowPrices" checked>
                                عرض الأسعار للزوار
                            </label>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="display: flex; align-items: center; gap: 10px; color: #2c3e50;">
                                <input type="checkbox" id="quickAnimations" checked>
                                تفعيل الرسوم المتحركة
                            </label>
                        </div>
                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button onclick="saveQuickSettings()" style="background: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">حفظ</button>
                            <button onclick="closeQuickSettings()" style="background: #95a5a6; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">إلغاء</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', quickSettingsHTML);

            // تحميل الإعدادات الحالية
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            if (settings.propertiesPerPage) {
                document.getElementById('quickPropertiesCount').value = settings.propertiesPerPage;
            }
            document.getElementById('quickShowPrices').checked = settings.showPrices !== false;
            document.getElementById('quickAnimations').checked = settings.enableAnimations !== false;
        }

        function saveQuickSettings() {
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            settings.propertiesPerPage = document.getElementById('quickPropertiesCount').value;
            settings.showPrices = document.getElementById('quickShowPrices').checked;
            settings.enableAnimations = document.getElementById('quickAnimations').checked;
            settings.lastUpdated = new Date().toISOString();

            localStorage.setItem('systemSettings', JSON.stringify(settings));

            const alert = document.createElement('div');
            alert.className = 'alert alert-success';
            alert.innerHTML = '<strong>تم الحفظ!</strong> تم حفظ الإعدادات السريعة بنجاح.';
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '10000';
            document.body.appendChild(alert);

            setTimeout(() => alert.remove(), 3000);
            closeQuickSettings();
        }

        function closeQuickSettings() {
            const modal = document.getElementById('quickSettingsModal');
            if (modal) {
                modal.remove();
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAuthentication()) {
                updateStats();
                console.log('🎛️ لوحة تحكم المسؤول جاهزة');
            }
        });
    </script>
</body>
</html>
