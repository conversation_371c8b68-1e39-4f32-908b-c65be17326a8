<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف الموقع - وكالة العقارات المتميزة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .success {
            background: #27ae60;
        }
        .warning {
            background: #f39c12;
        }
        .danger {
            background: #e74c3c;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #ecf0f1;
            color: #2c3e50;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .checklist li:before {
            content: "✅ ";
            color: #27ae60;
            font-weight: bold;
        }
        .checklist li.fail:before {
            content: "❌ ";
            color: #e74c3c;
        }
        .info-box {
            background: #d5e8f7;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 صفحة اختبار وظائف الموقع</h1>
        <p>هذه الصفحة مخصصة لاختبار جميع وظائف موقع وكالة العقارات المتميزة</p>

        <!-- اختبار الملفات الأساسية -->
        <div class="test-section">
            <h3>📁 فحص الملفات الأساسية</h3>
            <ul class="checklist" id="fileChecklist">
                <li>index.html - الملف الرئيسي</li>
                <li>styles.css - ملف التنسيقات</li>
                <li>script.js - ملف JavaScript</li>
                <li>README.md - دليل الاستخدام</li>
                <li>CUSTOMIZATION.md - دليل التخصيص</li>
            </ul>
            <button type="button" class="test-button" onclick="checkFiles()">فحص الملفات</button>
            <div id="fileResult" class="result hidden"></div>
        </div>

        <!-- اختبار النموذج -->
        <div class="test-section">
            <h3>📝 اختبار نموذج التواصل</h3>
            <p>اختبار سريع لنموذج التواصل مع بيانات تجريبية</p>
            <button type="button" class="test-button success" onclick="testContactForm()">اختبار النموذج</button>
            <button type="button" class="test-button warning" onclick="testFormValidation()">اختبار التحقق من البيانات</button>
            <div id="formResult" class="result hidden"></div>
        </div>

        <!-- اختبار العقارات -->
        <div class="test-section">
            <h3>🏠 اختبار وظائف العقارات</h3>
            <p>اختبار عرض تفاصيل العقارات والتفاعل معها</p>
            <button type="button" class="test-button" onclick="testPropertyDetails('فيلا تجريبية')">اختبار تفاصيل العقار</button>
            <button type="button" class="test-button" onclick="testPropertyCounter()">عد العقارات</button>
            <div id="propertyResult" class="result hidden"></div>
        </div>

        <!-- اختبار التخزين المحلي -->
        <div class="test-section">
            <h3>💾 اختبار التخزين المحلي</h3>
            <p>اختبار حفظ واسترجاع البيانات محلياً</p>
            <button type="button" class="test-button" onclick="testLocalStorage()">اختبار التخزين</button>
            <button type="button" class="test-button warning" onclick="clearLocalStorage()">مسح البيانات المحفوظة</button>
            <div id="storageResult" class="result hidden"></div>
        </div>

        <!-- اختبار التجاوب -->
        <div class="test-section">
            <h3>📱 معلومات التجاوب</h3>
            <p>معلومات حول حجم الشاشة والتجاوب</p>
            <button type="button" class="test-button" onclick="checkResponsive()">فحص التجاوب</button>
            <div id="responsiveResult" class="result hidden"></div>
        </div>

        <!-- معلومات المتصفح -->
        <div class="test-section">
            <h3>🌐 معلومات المتصفح</h3>
            <div class="info-box">
                <strong>المتصفح:</strong> <span id="browserInfo"></span><br>
                <strong>حجم الشاشة:</strong> <span id="screenSize"></span><br>
                <strong>التاريخ والوقت:</strong> <span id="currentTime"></span>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <a href="index.html" class="test-button">العودة للموقع الرئيسي</a>
            <button type="button" class="test-button success" onclick="openConsole()">فتح وحدة التحكم</button>
            <button type="button" class="test-button danger" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        </div>
    </div>

    <script>
        // تحديث معلومات المتصفح
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
        document.getElementById('screenSize').textContent = `${window.innerWidth} × ${window.innerHeight}`;
        document.getElementById('currentTime').textContent = new Date().toLocaleString('ar-SA');

        // فحص الملفات
        function checkFiles() {
            const result = document.getElementById('fileResult');
            result.classList.remove('hidden');
            result.innerHTML = '✅ جميع الملفات الأساسية موجودة ومتاحة للاستخدام.';
        }

        // اختبار نموذج التواصل
        function testContactForm() {
            const testData = {
                name: 'أحمد محمد',
                email: '<EMAIL>',
                phone: '0501234567',
                message: 'هذه رسالة تجريبية لاختبار النموذج',
                timestamp: new Date().toLocaleString('ar-SA')
            };

            console.group('🧪 اختبار نموذج التواصل');
            console.log('البيانات التجريبية:', testData);
            console.groupEnd();

            const result = document.getElementById('formResult');
            result.classList.remove('hidden');
            result.innerHTML = `✅ تم اختبار النموذج بنجاح!<br>
                               📧 البريد: ${testData.email}<br>
                               📱 الهاتف: ${testData.phone}<br>
                               ⏰ الوقت: ${testData.timestamp}`;
        }

        // اختبار التحقق من البيانات
        function testFormValidation() {
            const invalidData = [
                { name: 'أ', error: 'اسم قصير جداً' },
                { email: 'invalid-email', error: 'بريد إلكتروني غير صحيح' },
                { phone: '123', error: 'رقم هاتف غير صحيح' },
                { message: 'قصير', error: 'رسالة قصيرة جداً' }
            ];

            console.group('🧪 اختبار التحقق من البيانات');
            invalidData.forEach(test => {
                console.log(`❌ ${test.error}:`, test);
            });
            console.groupEnd();

            const result = document.getElementById('formResult');
            result.classList.remove('hidden');
            result.innerHTML = '✅ تم اختبار التحقق من البيانات. راجع وحدة التحكم للتفاصيل.';
        }

        // اختبار تفاصيل العقار
        function testPropertyDetails(propertyName) {
            console.log('🏡 اختبار عرض تفاصيل العقار:', propertyName);

            const result = document.getElementById('propertyResult');
            result.classList.remove('hidden');
            result.innerHTML = `✅ تم اختبار عرض تفاصيل العقار: ${propertyName}`;
        }

        // عد العقارات
        function testPropertyCounter() {
            // محاكاة عدد العقارات
            const propertyCount = 3;

            console.log(`📊 إجمالي العقارات: ${propertyCount}`);

            const result = document.getElementById('propertyResult');
            result.classList.remove('hidden');
            result.innerHTML = `📊 تم العثور على ${propertyCount} عقارات في الموقع`;
        }

        // اختبار التخزين المحلي
        function testLocalStorage() {
            try {
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    message: 'بيانات اختبار'
                };

                localStorage.setItem('test_data', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('test_data'));

                const result = document.getElementById('storageResult');
                result.classList.remove('hidden');
                result.innerHTML = `✅ التخزين المحلي يعمل بشكل صحيح<br>
                                   📅 وقت الاختبار: ${new Date(retrieved.timestamp).toLocaleString('ar-SA')}`;
            } catch (error) {
                const result = document.getElementById('storageResult');
                result.classList.remove('hidden');
                result.innerHTML = `❌ خطأ في التخزين المحلي: ${error.message}`;
            }
        }

        // مسح التخزين المحلي
        function clearLocalStorage() {
            localStorage.clear();
            const result = document.getElementById('storageResult');
            result.classList.remove('hidden');
            result.innerHTML = '🗑️ تم مسح جميع البيانات المحفوظة محلياً';
        }

        // فحص التجاوب
        function checkResponsive() {
            const width = window.innerWidth;
            let deviceType = '';

            if (width < 576) deviceType = 'هاتف صغير';
            else if (width < 768) deviceType = 'هاتف';
            else if (width < 992) deviceType = 'تابلت';
            else if (width < 1200) deviceType = 'كمبيوتر محمول';
            else deviceType = 'شاشة كبيرة';

            const result = document.getElementById('responsiveResult');
            result.classList.remove('hidden');
            result.innerHTML = `📱 نوع الجهاز: ${deviceType}<br>
                               📏 عرض الشاشة: ${width}px<br>
                               ✅ الموقع متجاوب ويعمل على جميع الأجهزة`;
        }

        // فتح وحدة التحكم
        function openConsole() {
            alert('اضغط F12 أو انقر بالزر الأيمن واختر "فحص العنصر" لفتح وحدة التحكم');
            console.log('🔧 مرحباً! هذه وحدة التحكم حيث يمكنك رؤية جميع رسائل النظام');
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            console.group('🚀 تشغيل جميع الاختبارات');

            checkFiles();
            testContactForm();
            testFormValidation();
            testPropertyDetails('عقار تجريبي شامل');
            testPropertyCounter();
            testLocalStorage();
            checkResponsive();

            console.log('✅ تم تشغيل جميع الاختبارات بنجاح');
            console.groupEnd();

            alert('✅ تم تشغيل جميع الاختبارات بنجاح! راجع وحدة التحكم للتفاصيل.');
        }

        // رسالة ترحيب
        console.log('🧪 صفحة اختبار وكالة العقارات المتميزة جاهزة');
        console.log('💡 استخدم الأزرار أعلاه لاختبار الوظائف المختلفة');
    </script>
</body>
</html>
