<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وكالة العقارات المتميزة - منزلك المثالي بانتظارك</title>
    <style>
        /* إعدادات عامة */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* رأس الصفحة */
        header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        nav a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        nav a:hover {
            color: #3498db;
        }

        /* القسم الرئيسي */
        .hero {
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23e8f4f8" width="1200" height="600"/><text x="600" y="300" text-anchor="middle" font-size="24" fill="%23666">صورة عقار فاخر - قابلة للتعديل</text></svg>');
            background-size: cover;
            background-position: center;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }

        .hero-content h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }

        .cta-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        /* قسم العقارات */
        .properties {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #2c3e50;
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .property-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .property-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .property-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #e8f4f8, #d1ecf1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .property-info {
            padding: 1.5rem;
        }

        .property-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 0.5rem;
        }

        .property-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .property-location {
            color: #7f8c8d;
            margin-bottom: 1rem;
        }

        .details-button {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s ease;
            width: 100%;
        }

        .details-button:hover {
            background: #2c3e50;
        }

        /* قسم حول الوكالة */
        .about {
            padding: 80px 0;
            background: white;
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .about-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
        }

        .about-image {
            width: 100%;
            height: 300px;
            background: linear-gradient(45deg, #e8f4f8, #d1ecf1);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .about-features {
            margin-top: 1rem;
            padding-right: 1rem;
        }

        /* قسم التواصل */
        .contact {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .contact-form {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-group textarea {
            height: 120px;
            resize: vertical;
        }

        .submit-button {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.3s ease;
        }

        .submit-button:hover {
            transform: translateY(-2px);
        }

        /* التذييل */
        footer {
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
            text-align: center;
        }

        .social-links {
            margin-bottom: 1rem;
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 1rem;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #3498db;
        }

        /* التجاوب */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            nav ul {
                gap: 1rem;
            }

            .hero-content h2 {
                font-size: 2rem;
            }

            .about-content {
                grid-template-columns: 1fr;
            }

            .properties-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <!-- يمكن تعديل مسار الصورة هنا -->
                    <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle fill='%233498db' cx='50' cy='50' r='50'/><text x='50' y='60' text-anchor='middle' font-size='40' fill='white'>🏠</text></svg>" alt="شعار الوكالة">
                    <h1>وكالة العقارات المتميزة</h1>
                </div>
                <nav>
                    <ul>
                        <li><a href="#home">الرئيسية</a></li>
                        <li><a href="#properties">العقارات</a></li>
                        <li><a href="#about">من نحن</a></li>
                        <li><a href="#contact">تواصل معنا</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- القسم الرئيسي -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h2>منزلك المثالي بانتظارك</h2>
            <p>نحن نساعدك في العثور على العقار المثالي الذي يناسب احتياجاتك وميزانيتك. خبرة واسعة وخدمة متميزة في عالم العقارات.</p>
            <a href="#properties" class="cta-button">استعرض العقارات</a>
        </div>
    </section>

    <!-- قسم العقارات -->
    <section id="properties" class="properties">
        <div class="container">
            <h2 class="section-title">عقاراتنا المميزة</h2>
            <div class="properties-grid">
                <!-- بطاقة عقار 1 -->
                <div class="property-card">
                    <div class="property-image">
                        صورة فيلا فاخرة - قابلة للتعديل
                    </div>
                    <div class="property-info">
                        <div class="property-price">2,500,000 درهم</div>
                        <div class="property-title">فيلا فاخرة مع حديقة</div>
                        <div class="property-location">📍 الرباط - حي الرياض</div>
                        <button type="button" class="details-button" onclick="showPropertyDetails('فيلا فاخرة مع حديقة')">تفاصيل أكثر</button>
                    </div>
                </div>

                <!-- بطاقة عقار 2 -->
                <div class="property-card">
                    <div class="property-image">
                        صورة شقة عصرية - قابلة للتعديل
                    </div>
                    <div class="property-info">
                        <div class="property-price">950,000 درهم</div>
                        <div class="property-title">شقة عصرية 3 غرف</div>
                        <div class="property-location">📍 الدار البيضاء - حي المعاريف</div>
                        <button type="button" class="details-button" onclick="showPropertyDetails('شقة عصرية 3 غرف')">تفاصيل أكثر</button>
                    </div>
                </div>

                <!-- بطاقة عقار 3 -->
                <div class="property-card">
                    <div class="property-image">
                        صورة دوبلكس - قابلة للتعديل
                    </div>
                    <div class="property-info">
                        <div class="property-price">1,650,000 درهم</div>
                        <div class="property-title">دوبلكس مع تراس</div>
                        <div class="property-location">📍 مراكش - حي جيليز</div>
                        <button type="button" class="details-button" onclick="showPropertyDetails('دوبلكس مع تراس')">تفاصيل أكثر</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم حول الوكالة -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">من نحن</h2>
            <div class="about-content">
                <div class="about-text">
                    <h3>وكالة العقارات المتميزة</h3>
                    <p>
                        نحن وكالة عقارية رائدة في المملكة العربية السعودية، نتميز بخبرة تزيد عن 15 عامًا في مجال العقارات.
                        نقدم خدمات شاملة تشمل بيع وشراء وتأجير العقارات السكنية والتجارية والاستثمارية.
                    </p>
                    <p>
                        فريقنا المتخصص يضم خبراء في التقييم العقاري والاستشارات القانونية، مما يضمن لعملائنا الحصول على أفضل الصفقات
                        وأكثرها أمانًا. نؤمن بأن كل عميل يستحق الأفضل، ولذلك نسعى دائمًا لتقديم خدمة متميزة تفوق التوقعات.
                    </p>
                    <ul class="about-features">
                        <li>✅ خبرة أكثر من 15 عامًا</li>
                        <li>✅ فريق متخصص ومؤهل</li>
                        <li>✅ خدمة عملاء متميزة</li>
                        <li>✅ أسعار تنافسية</li>
                    </ul>
                </div>
                <div class="about-image">
                    صورة أو فيديو تعريفي - قابل للتعديل
                </div>
            </div>
        </div>
    </section>

    <!-- قسم التواصل -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>
            <form class="contact-form" id="contactForm">
                <div class="form-group">
                    <label for="name">الاسم الكامل</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
                <div class="form-group">
                    <label for="message">الرسالة</label>
                    <textarea id="message" name="message" placeholder="اكتب رسالتك هنا..." required></textarea>
                </div>
                <button type="submit" class="submit-button">إرسال الرسالة</button>
            </form>
        </div>
    </section>

    <!-- التذييل -->
    <footer>
        <div class="container">
            <div class="social-links">
                <a href="#" title="Facebook">📘</a>
                <a href="#" title="Instagram">📷</a>
                <a href="#" title="WhatsApp">💬</a>
                <a href="#" title="Twitter">🐦</a>
            </div>
            <p>&copy; 2024 وكالة العقارات المتميزة. جميع الحقوق محفوظة.</p>
            <p>تصميم وتطوير: فريق التطوير المتخصص</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // تأثير التمرير السلس للروابط
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // معالج نموذج التواصل
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // جمع بيانات النموذج
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                message: document.getElementById('message').value
            };

            // طباعة البيانات في وحدة التحكم
            console.log('بيانات التواصل المرسلة:');
            console.log('الاسم:', formData.name);
            console.log('البريد الإلكتروني:', formData.email);
            console.log('رقم الهاتف:', formData.phone);
            console.log('الرسالة:', formData.message);
            console.log('-------------------');

            // عرض رسالة تأكيد
            alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريبًا.');

            // إعادة تعيين النموذج
            this.reset();
        });

        // دالة عرض تفاصيل العقار
        function showPropertyDetails(propertyName) {
            const properties = {
                'فيلا فاخرة مع حديقة': {
                    price: '2,500,000 درهم',
                    location: 'الرباط - حي الرياض',
                    area: '500 متر مربع',
                    rooms: '5 غرف نوم',
                    bathrooms: '4 حمامات',
                    features: ['حديقة خاصة', 'مسبح', 'موقف سيارتين', 'مطبخ مجهز']
                },
                'شقة عصرية 3 غرف': {
                    price: '950,000 درهم',
                    location: 'الدار البيضاء - حي المعاريف',
                    area: '120 متر مربع',
                    rooms: '3 غرف نوم',
                    bathrooms: '2 حمام',
                    features: ['شرفة', 'مطبخ مجهز', 'موقف سيارة', 'أمن 24 ساعة']
                },
                'دوبلكس مع تراس': {
                    price: '1,650,000 درهم',
                    location: 'مراكش - حي جيليز',
                    area: '200 متر مربع',
                    rooms: '4 غرف نوم',
                    bathrooms: '3 حمامات',
                    features: ['تراس كبير', 'إطلالة جبلية', 'موقف سيارتين', 'مطبخ مفتوح']
                }
            };

            const property = properties[propertyName];
            if (property) {
                const details = `🏠 ${propertyName}\n\n` +
                              `💰 السعر: ${property.price}\n` +
                              `📍 الموقع: ${property.location}\n` +
                              `📐 المساحة: ${property.area}\n` +
                              `🛏️ ${property.rooms}\n` +
                              `🚿 ${property.bathrooms}\n\n` +
                              `✨ المميزات:\n${property.features.map(f => `• ${f}`).join('\n')}\n\n` +
                              `📞 للاستفسار والمعاينة اتصل بنا الآن!`;
                alert(details);
            } else {
                alert(`تفاصيل ${propertyName}\n\nهذه الميزة ستكون متاحة قريبًا مع صفحة تفاصيل مخصصة لكل عقار.`);
            }
            console.log('تم النقر على تفاصيل العقار:', propertyName);
        }

        // تأثير إخفاء/إظهار الهيدر عند التمرير
        let lastScrollTop = 0;
        const header = document.querySelector('header');

        window.addEventListener('scroll', function() {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // التمرير لأسفل - إخفاء الهيدر
                header.style.transform = 'translateY(-100%)';
            } else {
                // التمرير لأعلى - إظهار الهيدر
                header.style.transform = 'translateY(0)';
            }

            lastScrollTop = scrollTop;
        });

        // تأثير الظهور التدريجي للعناصر عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // تحميل المحتوى المخصص من لوحة التحكم
        function loadCustomContent() {
            const savedSettings = localStorage.getItem('siteSettings');
            const savedProperties = localStorage.getItem('siteProperties');

            if (savedSettings) {
                try {
                    const settings = JSON.parse(savedSettings);

                    // تحديث معلومات الوكالة
                    if (settings.agencyName) {
                        document.querySelector('.logo h1').textContent = settings.agencyName;
                        document.title = settings.agencyName + ' - ' + (settings.agencySlogan || 'منزلك المثالي بانتظارك');
                    }

                    if (settings.agencySlogan) {
                        document.querySelector('.hero-content h2').textContent = settings.agencySlogan;
                    }

                    if (settings.agencyDescription) {
                        document.querySelector('.hero-content p').textContent = settings.agencyDescription;
                    }

                    // تحديث قسم "من نحن"
                    if (settings.aboutTitle) {
                        document.querySelector('.about-text h3').textContent = settings.aboutTitle;
                    }

                    if (settings.aboutText1) {
                        document.querySelector('.about-text p:first-of-type').textContent = settings.aboutText1;
                    }

                    if (settings.aboutText2) {
                        document.querySelector('.about-text p:last-of-type').textContent = settings.aboutText2;
                    }

                    // تحديث الألوان
                    if (settings.primaryColor || settings.secondaryColor || settings.accentColor) {
                        const style = document.createElement('style');
                        style.textContent = `
                            header { background: linear-gradient(135deg, ${settings.secondaryColor || '#2c3e50'}, #34495e); }
                            .cta-button { background: linear-gradient(45deg, ${settings.primaryColor || '#3498db'}, #2980b9); }
                            .property-price { color: ${settings.accentColor || '#27ae60'}; }
                            .submit-button { background: linear-gradient(45deg, ${settings.accentColor || '#27ae60'}, #2ecc71); }
                        `;
                        document.head.appendChild(style);
                    }

                    console.log('✅ تم تحميل الإعدادات المخصصة');
                } catch (error) {
                    console.warn('⚠️ خطأ في تحميل الإعدادات:', error);
                }
            }

            // تحميل العقارات المخصصة
            if (savedProperties) {
                try {
                    const properties = JSON.parse(savedProperties);
                    if (properties.length > 0) {
                        updatePropertiesDisplay(properties);
                        console.log('✅ تم تحميل العقارات المخصصة');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في تحميل العقارات:', error);
                }
            }
        }

        // تحديث عرض العقارات
        function updatePropertiesDisplay(properties) {
            const propertiesGrid = document.querySelector('.properties-grid');
            if (!propertiesGrid) return;

            let html = '';
            properties.forEach(property => {
                html += `
                    <div class="property-card">
                        <div class="property-image">
                            ${property.image ?
                                `<img src="${property.image}" alt="${property.title}" style="width: 100%; height: 100%; object-fit: cover;">` :
                                property.title + ' - قابلة للتعديل'
                            }
                        </div>
                        <div class="property-info">
                            <div class="property-price">${property.price}</div>
                            <div class="property-title">${property.title}</div>
                            <div class="property-location">📍 ${property.location}</div>
                            <button class="details-button" onclick="showPropertyDetails('${property.title}')">تفاصيل أكثر</button>
                        </div>
                    </div>
                `;
            });

            propertiesGrid.innerHTML = html;

            // إعادة تطبيق تأثيرات الظهور
            setTimeout(() => {
                const propertyCards = document.querySelectorAll('.property-card');
                propertyCards.forEach(card => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    observer.observe(card);
                });
            }, 100);
        }

        // إضافة رابط لوحة التحكم (مخفي)
        function addAdminLink() {
            // إضافة رابط مخفي للوحة التحكم (يظهر عند الضغط على Ctrl+Alt+A)
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.altKey && e.key === 'a') {
                    e.preventDefault();
                    const password = prompt('كلمة مرور المسؤول:');
                    if (password === 'admin123') {
                        window.open('admin-login.html', '_blank');
                    } else if (password !== null) {
                        alert('كلمة مرور خاطئة');
                    }
                }
            });

            // إضافة رابط في التذييل (مخفي)
            const footer = document.querySelector('footer .container');
            if (footer) {
                const adminLink = document.createElement('div');
                adminLink.style.cssText = 'margin-top: 1rem; opacity: 0.3; font-size: 0.7rem;';
                adminLink.innerHTML = '<a href="admin-login.html" style="color: #7f8c8d; text-decoration: none;">لوحة التحكم</a>';
                footer.appendChild(adminLink);
            }
        }

        // تطبيق التأثير على بطاقات العقارات
        document.addEventListener('DOMContentLoaded', function() {
            // تحميل المحتوى المخصص أولاً
            loadCustomContent();

            // ثم تطبيق التأثيرات
            const propertyCards = document.querySelectorAll('.property-card');
            propertyCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // إضافة رابط لوحة التحكم
            addAdminLink();
        });

        // إضافة تأثير التحميل
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
        });

        // تأثير التحميل الأولي
        document.body.style.opacity = '0';
        document.body.style.transition = 'opacity 0.5s ease';
    </script>
</body>
</html>
