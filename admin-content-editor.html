<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محرر المحتوى - وكالة العقارات المتميزة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .editor-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: #3498db;
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .section-content {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-group textarea {
            height: 120px;
            resize: vertical;
        }

        .form-group textarea.large {
            height: 200px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-left: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .property-editor {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .property-editor h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #3498db;
        }

        .image-upload {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .image-upload.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .preview-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .color-picker {
            width: 60px;
            height: 40px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .grid-2,
            .grid-3 {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 0 0.5rem;
            }

            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #7f8c8d;
        }

        .loading.show {
            display: block;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            display: inline-block;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>محرر محتوى الموقع</h1>
        <div>
            <button class="btn btn-success" onclick="saveAllChanges()">حفظ جميع التغييرات</button>
            <button class="btn btn-secondary" onclick="previewChanges()">معاينة</button>
            <button class="btn btn-danger" onclick="window.close()">إغلاق</button>
        </div>
    </div>

    <div class="container">
        <div class="alert alert-info">
            <strong>تعليمات:</strong> يمكنك تعديل محتوى الموقع من هنا. جميع التغييرات سيتم حفظها محلياً وتطبيقها على الموقع الرئيسي.
        </div>

        <!-- تحرير معلومات الوكالة -->
        <div class="editor-section">
            <div class="section-header">معلومات الوكالة الأساسية</div>
            <div class="section-content">
                <div class="grid-2">
                    <div class="form-group">
                        <label for="agencyName">اسم الوكالة</label>
                        <input type="text" id="agencyName" value="وكالة العقارات المتميزة">
                    </div>
                    <div class="form-group">
                        <label for="agencySlogan">الشعار النصي</label>
                        <input type="text" id="agencySlogan" value="منزلك المثالي بانتظارك">
                    </div>
                </div>
                <div class="form-group">
                    <label for="agencyDescription">وصف الوكالة</label>
                    <textarea id="agencyDescription" class="large">نحن نساعدك في العثور على العقار المثالي الذي يناسب احتياجاتك وميزانيتك. خبرة واسعة وخدمة متميزة في عالم العقارات.</textarea>
                </div>
            </div>
        </div>

        <!-- تحرير قسم "من نحن" -->
        <div class="editor-section">
            <div class="section-header">قسم "من نحن"</div>
            <div class="section-content">
                <div class="form-group">
                    <label for="aboutTitle">عنوان القسم</label>
                    <input type="text" id="aboutTitle" value="وكالة العقارات المتميزة">
                </div>
                <div class="form-group">
                    <label for="aboutText1">الفقرة الأولى</label>
                    <textarea id="aboutText1" class="large">نحن وكالة عقارية رائدة في المملكة العربية السعودية، نتميز بخبرة تزيد عن 15 عامًا في مجال العقارات. نقدم خدمات شاملة تشمل بيع وشراء وتأجير العقارات السكنية والتجارية والاستثمارية.</textarea>
                </div>
                <div class="form-group">
                    <label for="aboutText2">الفقرة الثانية</label>
                    <textarea id="aboutText2" class="large">فريقنا المتخصص يضم خبراء في التقييم العقاري والاستشارات القانونية، مما يضمن لعملائنا الحصول على أفضل الصفقات وأكثرها أمانًا. نؤمن بأن كل عميل يستحق الأفضل، ولذلك نسعى دائمًا لتقديم خدمة متميزة تفوق التوقعات.</textarea>
                </div>
            </div>
        </div>

        <!-- تحرير العقارات -->
        <div class="editor-section">
            <div class="section-header">إدارة العقارات</div>
            <div class="section-content">
                <div class="alert alert-warning">
                    <strong>ملاحظة:</strong> يمكنك تعديل العقارات الموجودة أو إضافة عقارات جديدة.
                </div>
                
                <button class="btn btn-primary" onclick="addNewProperty()">إضافة عقار جديد</button>
                
                <div id="propertiesEditor">
                    <!-- سيتم تحميل العقارات هنا -->
                </div>
            </div>
        </div>

        <!-- تحرير الألوان والتصميم -->
        <div class="editor-section">
            <div class="section-header">ألوان الموقع</div>
            <div class="section-content">
                <div class="grid-3">
                    <div class="form-group">
                        <label for="primaryColor">اللون الأساسي</label>
                        <input type="color" id="primaryColor" class="color-picker" value="#3498db">
                    </div>
                    <div class="form-group">
                        <label for="secondaryColor">اللون الثانوي</label>
                        <input type="color" id="secondaryColor" class="color-picker" value="#2c3e50">
                    </div>
                    <div class="form-group">
                        <label for="accentColor">لون التمييز</label>
                        <input type="color" id="accentColor" class="color-picker" value="#27ae60">
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات التواصل -->
        <div class="editor-section">
            <div class="section-header">معلومات التواصل</div>
            <div class="section-content">
                <div class="grid-2">
                    <div class="form-group">
                        <label for="contactPhone">رقم الهاتف</label>
                        <input type="tel" id="contactPhone" value="+966501234567">
                    </div>
                    <div class="form-group">
                        <label for="contactEmail">البريد الإلكتروني</label>
                        <input type="email" id="contactEmail" value="<EMAIL>">
                    </div>
                </div>
                <div class="form-group">
                    <label for="contactAddress">العنوان</label>
                    <input type="text" id="contactAddress" value="الرياض، المملكة العربية السعودية">
                </div>
            </div>
        </div>

        <div class="loading" id="loading">جاري حفظ التغييرات...</div>
    </div>

    <script>
        // بيانات افتراضية للعقارات
        let properties = [
            {
                id: 1,
                title: "فيلا فاخرة مع حديقة",
                price: "1,200,000 ريال",
                location: "الرياض - حي النرجس",
                image: "",
                description: "فيلا فاخرة بتصميم عصري مع حديقة واسعة"
            },
            {
                id: 2,
                title: "شقة عصرية 3 غرف",
                price: "450,000 ريال",
                location: "جدة - حي الزهراء",
                image: "",
                description: "شقة عصرية بتشطيب راقي في موقع متميز"
            },
            {
                id: 3,
                title: "دوبلكس مع تراس",
                price: "800,000 ريال",
                location: "الدمام - حي الشاطئ",
                image: "",
                description: "دوبلكس واسع مع تراس مطل على البحر"
            }
        ];

        // تحميل البيانات المحفوظة
        function loadSavedData() {
            const savedProperties = localStorage.getItem('siteProperties');
            if (savedProperties) {
                properties = JSON.parse(savedProperties);
            }

            const savedSettings = localStorage.getItem('siteSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                
                // تحميل الإعدادات في النموذج
                if (settings.agencyName) document.getElementById('agencyName').value = settings.agencyName;
                if (settings.agencySlogan) document.getElementById('agencySlogan').value = settings.agencySlogan;
                if (settings.agencyDescription) document.getElementById('agencyDescription').value = settings.agencyDescription;
                if (settings.aboutTitle) document.getElementById('aboutTitle').value = settings.aboutTitle;
                if (settings.aboutText1) document.getElementById('aboutText1').value = settings.aboutText1;
                if (settings.aboutText2) document.getElementById('aboutText2').value = settings.aboutText2;
                if (settings.primaryColor) document.getElementById('primaryColor').value = settings.primaryColor;
                if (settings.secondaryColor) document.getElementById('secondaryColor').value = settings.secondaryColor;
                if (settings.accentColor) document.getElementById('accentColor').value = settings.accentColor;
                if (settings.contactPhone) document.getElementById('contactPhone').value = settings.contactPhone;
                if (settings.contactEmail) document.getElementById('contactEmail').value = settings.contactEmail;
                if (settings.contactAddress) document.getElementById('contactAddress').value = settings.contactAddress;
            }

            renderPropertiesEditor();
        }

        // عرض محرر العقارات
        function renderPropertiesEditor() {
            const container = document.getElementById('propertiesEditor');
            let html = '';

            properties.forEach((property, index) => {
                html += `
                    <div class="property-editor">
                        <h4>عقار ${index + 1}</h4>
                        <div class="grid-2">
                            <div class="form-group">
                                <label>عنوان العقار</label>
                                <input type="text" value="${property.title}" onchange="updateProperty(${index}, 'title', this.value)">
                            </div>
                            <div class="form-group">
                                <label>السعر</label>
                                <input type="text" value="${property.price}" onchange="updateProperty(${index}, 'price', this.value)">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>الموقع</label>
                            <input type="text" value="${property.location}" onchange="updateProperty(${index}, 'location', this.value)">
                        </div>
                        <div class="form-group">
                            <label>الوصف</label>
                            <textarea onchange="updateProperty(${index}, 'description', this.value)">${property.description}</textarea>
                        </div>
                        <div class="form-group">
                            <label>صورة العقار</label>
                            <div class="image-upload" onclick="selectImage(${index})">
                                <p>انقر لاختيار صورة أو اسحب الصورة هنا</p>
                                ${property.image ? `<img src="${property.image}" class="preview-image" alt="معاينة">` : ''}
                            </div>
                        </div>
                        <button class="btn btn-danger" onclick="deleteProperty(${index})">حذف العقار</button>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // تحديث بيانات العقار
        function updateProperty(index, field, value) {
            properties[index][field] = value;
        }

        // إضافة عقار جديد
        function addNewProperty() {
            const newProperty = {
                id: Date.now(),
                title: "عقار جديد",
                price: "0 ريال",
                location: "الموقع",
                image: "",
                description: "وصف العقار"
            };
            
            properties.push(newProperty);
            renderPropertiesEditor();
        }

        // حذف عقار
        function deleteProperty(index) {
            if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
                properties.splice(index, 1);
                renderPropertiesEditor();
            }
        }

        // اختيار صورة
        function selectImage(index) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        properties[index].image = e.target.result;
                        renderPropertiesEditor();
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        // حفظ جميع التغييرات
        function saveAllChanges() {
            const loading = document.getElementById('loading');
            loading.classList.add('show');

            // جمع جميع البيانات
            const settings = {
                agencyName: document.getElementById('agencyName').value,
                agencySlogan: document.getElementById('agencySlogan').value,
                agencyDescription: document.getElementById('agencyDescription').value,
                aboutTitle: document.getElementById('aboutTitle').value,
                aboutText1: document.getElementById('aboutText1').value,
                aboutText2: document.getElementById('aboutText2').value,
                primaryColor: document.getElementById('primaryColor').value,
                secondaryColor: document.getElementById('secondaryColor').value,
                accentColor: document.getElementById('accentColor').value,
                contactPhone: document.getElementById('contactPhone').value,
                contactEmail: document.getElementById('contactEmail').value,
                contactAddress: document.getElementById('contactAddress').value,
                lastUpdated: new Date().toISOString()
            };

            // حفظ البيانات
            setTimeout(() => {
                localStorage.setItem('siteSettings', JSON.stringify(settings));
                localStorage.setItem('siteProperties', JSON.stringify(properties));
                
                loading.classList.remove('show');
                
                // عرض رسالة نجاح
                const alert = document.createElement('div');
                alert.className = 'alert alert-success';
                alert.innerHTML = '<strong>تم الحفظ!</strong> تم حفظ جميع التغييرات بنجاح.';
                document.querySelector('.container').insertBefore(alert, document.querySelector('.editor-section'));
                
                setTimeout(() => {
                    alert.remove();
                }, 3000);
                
                console.log('تم حفظ التغييرات:', { settings, properties });
            }, 1000);
        }

        // معاينة التغييرات
        function previewChanges() {
            saveAllChanges();
            setTimeout(() => {
                window.open('index.html', '_blank');
            }, 1500);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedData();
            console.log('🎨 محرر المحتوى جاهز');
        });

        // حفظ تلقائي كل 30 ثانية
        setInterval(() => {
            saveAllChanges();
        }, 30000);
    </script>
</body>
</html>
